import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../services/service_locator.dart';
import '../config/app_theme.dart';
import 'svg_document.dart';
import 'svg_editor_screen.dart';
import '../content/content_save_button.dart';
import '../models/content_item.dart';

/// SVG管理屏幕
class SvgManagerScreen extends StatefulWidget {
  /// 构造函数
  const SvgManagerScreen({super.key});

  @override
  State<SvgManagerScreen> createState() => _SvgManagerScreenState();
}

class _SvgManagerScreenState extends State<SvgManagerScreen> {
  /// SVG服务
  final _svgService = ServiceLocator().svgService;

  /// 是否正在加载
  bool _isLoading = true;

  /// 文档列表
  List<SvgDocument> _documents = [];

  /// 当前选中的文档
  SvgDocument? _selectedDocument;

  @override
  void initState() {
    super.initState();
    _loadDocuments();
  }

  /// 加载文档
  Future<void> _loadDocuments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 确保SVG服务已初始化
      await _svgService.initialize();

      setState(() {
        _documents = _svgService.documents;
        // 如果有文档则选择第一个
        _selectedDocument = _documents.isNotEmpty ? _documents.first : null;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      _showSnackBar('加载文档失败: $e');
    }
  }

  /// 导入SVG文件
  Future<void> _importSvgFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['svg'],
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _isLoading = true;
        });

        for (final file in result.files) {
          if (file.path != null) {
            await _svgService.importFromFile(File(file.path!));
          }
        }

        await _loadDocuments();
      }
    } catch (e) {
      _showSnackBar('导入SVG文件失败: $e');
    }
  }

  /// 创建新SVG
  void _createNewSvg() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SvgEditorScreen()),
    ).then((_) => _loadDocuments());
  }

  /// 编辑SVG
  void _editSvg(SvgDocument document) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => SvgEditorScreen(
              documentId: document.id,
              initialSvgContent: document.content,
              initialTitle: document.title,
            ),
      ),
    ).then((_) => _loadDocuments());
  }

  /// 删除SVG
  Future<void> _deleteSvg(SvgDocument document) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('确认删除'),
            content: Text('确定要删除 "${document.title}" 吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('删除'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        await _svgService.deleteDocument(document.id);
        await _loadDocuments();
      } catch (e) {
        setState(() {
          _isLoading = false;
        });

        _showSnackBar('删除文档失败: $e');
      }
    }
  }

  /// 分享SVG
  Future<void> _shareSvg(SvgDocument document) async {
    try {
      await document.share();
    } catch (e) {
      _showSnackBar('分享SVG失败: $e');
    }
  }

  /// 分享为PNG
  Future<void> _shareAsPng(SvgDocument document) async {
    try {
      await _svgService.shareAsPng(document);
    } catch (e) {
      _showSnackBar('分享PNG失败: $e');
    }
  }

  /// 显示提示消息
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('SVG管理'),
        elevation: 0,
        scrolledUnderElevation: 2,
        actions: [
          if (_selectedDocument != null)
            ContentSaveButton(
              title: _selectedDocument!.title,
              content: _selectedDocument!.content,
              contentType: ContentType.svg,
              onSaved: (item) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('SVG已保存到内容库'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
            ),
          IconButton(
            icon: Icon(Icons.add, color: theme.colorScheme.primary),
            onPressed: _createNewSvg,
            tooltip: '新建SVG',
          ),
          IconButton(
            icon: Icon(Icons.download, color: theme.colorScheme.primary),
            onPressed: _importSvgFile,
            tooltip: '导入SVG',
          ),
        ],
      ),
      body:
          _isLoading
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text('加载中...', style: theme.textTheme.bodyMedium),
                  ],
                ),
              )
              : _documents.isEmpty
              ? _buildEmptyState()
              : _buildDocumentsList(),
      floatingActionButton:
          _documents.isEmpty
              ? SizedBox.shrink()
              : FloatingActionButton.extended(
                onPressed: _createNewSvg,
                tooltip: '创建新SVG',
                icon: const Icon(Icons.add),
                label: const Text('创建新SVG'),
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
    );
  }

  Widget _buildEmptyState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Container(
        padding: const EdgeInsets.all(24),
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_outlined,
              size: 80,
              color:
                  isDarkMode
                      ? AppTheme.darkTextLightColor
                      : AppTheme.textLightColor,
            ),
            const SizedBox(height: 24),
            Text(
              '没有SVG文档',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color:
                    isDarkMode
                        ? AppTheme.darkTextColor
                        : AppTheme.textDarkColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '创建新的SVG文档或导入现有文件开始使用',
              textAlign: TextAlign.center,
              style: TextStyle(
                color:
                    isDarkMode
                        ? AppTheme.darkTextMediumColor
                        : AppTheme.textMediumColor,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _createNewSvg,
              icon: const Icon(Icons.add),
              label: const Text('创建新SVG'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                minimumSize: const Size(200, 48),
              ),
            ),
            const SizedBox(height: 16),
            OutlinedButton.icon(
              onPressed: _importSvgFile,
              icon: const Icon(Icons.file_upload_outlined),
              label: const Text('导入SVG文件'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                minimumSize: const Size(200, 48),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentsList() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.85,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: _documents.length,
        itemBuilder: (context, index) {
          final document = _documents[index];

          return Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
              side: BorderSide(
                color:
                    isDarkMode
                        ? AppTheme.darkBorderColor
                        : AppTheme.borderColor,
              ),
            ),
            clipBehavior: Clip.antiAlias,
            child: InkWell(
              onTap: () => _editSvg(document),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 预览区域
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color:
                            isDarkMode
                                ? AppTheme.darkBgColor
                                : AppTheme.bgLightColor,
                        border: Border(
                          bottom: BorderSide(
                            color:
                                isDarkMode
                                    ? AppTheme.darkBorderColor
                                    : AppTheme.borderColor,
                          ),
                        ),
                      ),
                      child: Center(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: SvgPicture.string(
                            document.content,
                            placeholderBuilder:
                                (context) => const CircularProgressIndicator(),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // 信息区域
                  Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          document.title,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color:
                                isDarkMode
                                    ? AppTheme.darkTextColor
                                    : AppTheme.textDarkColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '创建于: ${_formatDate(document.createdAt)}',
                          style: TextStyle(
                            fontSize: 12,
                            color:
                                isDarkMode
                                    ? AppTheme.darkTextMediumColor
                                    : AppTheme.textMediumColor,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            _buildActionIconButton(
                              icon: Icons.edit_outlined,
                              tooltip: '编辑',
                              onPressed: () => _editSvg(document),
                            ),
                            _buildActionIconButton(
                              icon: Icons.share_outlined,
                              tooltip: '分享',
                              onPressed: () => _shareSvg(document),
                            ),
                            _buildActionIconButton(
                              icon: Icons.image_outlined,
                              tooltip: '分享为PNG',
                              onPressed: () => _shareAsPng(document),
                            ),
                            _buildActionIconButton(
                              icon: Icons.delete_outline,
                              tooltip: '删除',
                              onPressed: () => _deleteSvg(document),
                              color: AppTheme.redDark,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionIconButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    Color? color,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final iconColor =
        color ?? (isDarkMode ? AppTheme.darkTextColor : AppTheme.textDarkColor);

    return IconButton(
      icon: Icon(icon, size: 20),
      onPressed: onPressed,
      tooltip: tooltip,
      color: iconColor,
      constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
      padding: EdgeInsets.zero,
      visualDensity: VisualDensity.compact,
    );
  }

  String _formatDate(DateTime dateTime) {
    final localDate = dateTime.toLocal();
    return '${localDate.year}-${localDate.month.toString().padLeft(2, '0')}-${localDate.day.toString().padLeft(2, '0')}';
  }
}
