import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../services/service_locator.dart';
import '../config/app_theme.dart';
import 'svg_document.dart';
import 'svg_editor_screen.dart';
import '../content/content_save_button.dart';
import '../models/content_item.dart';

/// SVG管理屏幕
class SvgManagerScreen extends StatefulWidget {
  /// 构造函数
  const SvgManagerScreen({super.key});

  @override
  State<SvgManagerScreen> createState() => _SvgManagerScreenState();
}

class _SvgManagerScreenState extends State<SvgManagerScreen> {
  /// SVG服务
  final _svgService = ServiceLocator().svgService;

  /// 是否正在加载
  bool _isLoading = true;

  /// 文档列表
  List<SvgDocument> _documents = [];

  /// 当前选中的文档
  SvgDocument? _selectedDocument;

  @override
  void initState() {
    super.initState();
    _loadDocuments();
  }

  /// 加载文档
  Future<void> _loadDocuments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 确保SVG服务已初始化
      await _svgService.initialize();

      setState(() {
        _documents = _svgService.documents;
        // 如果有文档则选择第一个
        _selectedDocument = _documents.isNotEmpty ? _documents.first : null;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      _showSnackBar('加载文档失败: $e');
    }
  }

  /// 导入SVG文件
  Future<void> _importSvgFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['svg'],
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _isLoading = true;
        });

        for (final file in result.files) {
          if (file.path != null) {
            await _svgService.importFromFile(File(file.path!));
          }
        }

        await _loadDocuments();
      }
    } catch (e) {
      _showSnackBar('导入SVG文件失败: $e');
    }
  }

  /// 创建新SVG
  void _createNewSvg() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SvgEditorScreen()),
    ).then((_) => _loadDocuments());
  }

  /// 编辑SVG
  void _editSvg(SvgDocument document) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => SvgEditorScreen(
              documentId: document.id,
              initialSvgContent: document.content,
              initialTitle: document.title,
            ),
      ),
    ).then((_) => _loadDocuments());
  }

  /// 删除SVG
  Future<void> _deleteSvg(SvgDocument document) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('确认删除'),
            content: Text('确定要删除 "${document.title}" 吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('删除'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        await _svgService.deleteDocument(document.id);
        await _loadDocuments();
      } catch (e) {
        setState(() {
          _isLoading = false;
        });

        _showSnackBar('删除文档失败: $e');
      }
    }
  }

  /// 分享SVG
  Future<void> _shareSvg(SvgDocument document) async {
    try {
      await document.share();
    } catch (e) {
      _showSnackBar('分享SVG失败: $e');
    }
  }

  /// 分享为PNG
  Future<void> _shareAsPng(SvgDocument document) async {
    try {
      await _svgService.shareAsPng(document);
    } catch (e) {
      _showSnackBar('分享PNG失败: $e');
    }
  }

  /// 显示提示消息
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor:
          isDarkMode ? AppTheme.darkBgColor : AppTheme.bgLightColor,
      appBar: AppBar(
        title: const Text(
          'SVG管理',
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 20),
        ),
        backgroundColor:
            isDarkMode ? AppTheme.darkBgLightColor : AppTheme.bgWhiteColor,
        foregroundColor:
            isDarkMode ? AppTheme.darkTextColor : AppTheme.textDarkColor,
        elevation: 0,
        scrolledUnderElevation: 1,
        shadowColor:
            isDarkMode ? AppTheme.darkBorderColor : AppTheme.borderColor,
        actions: [
          if (_selectedDocument != null)
            ContentSaveButton(
              title: _selectedDocument!.title,
              content: _selectedDocument!.content,
              contentType: ContentType.svg,
              onSaved: (item) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('SVG已保存到内容库'),
                    backgroundColor: AppTheme.greenDark,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppTheme.borderRadiusSM,
                      ),
                    ),
                  ),
                );
              },
            ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSM),
            ),
            child: IconButton(
              icon: const Icon(Icons.add, size: 22),
              onPressed: _createNewSvg,
              tooltip: '新建SVG',
              color: AppTheme.primaryColor,
            ),
          ),
          Container(
            margin: const EdgeInsets.only(right: 8, left: 4),
            decoration: BoxDecoration(
              color: AppTheme.secondaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSM),
            ),
            child: IconButton(
              icon: const Icon(Icons.download, size: 22),
              onPressed: _importSvgFile,
              tooltip: '导入SVG',
              color: AppTheme.secondaryColor,
            ),
          ),
        ],
      ),
      body:
          _isLoading
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text('加载中...', style: theme.textTheme.bodyMedium),
                  ],
                ),
              )
              : _documents.isEmpty
              ? _buildEmptyState()
              : _buildDocumentsList(),
      floatingActionButton:
          _documents.isEmpty
              ? null
              : Container(
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryColor.withValues(alpha: 0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: FloatingActionButton.extended(
                  onPressed: _createNewSvg,
                  tooltip: '创建新SVG',
                  icon: const Icon(Icons.add, size: 20),
                  label: const Text(
                    '创建新SVG',
                    style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
                  ),
                  backgroundColor: Colors.transparent,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      AppTheme.borderRadiusMD,
                    ),
                  ),
                ),
              ),
    );
  }

  Widget _buildEmptyState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Container(
        padding: const EdgeInsets.all(32),
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 现代化的图标设计
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: AppTheme.purpleGradient,
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusXL),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.purpleDark.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: const Icon(
                Icons.image_outlined,
                size: 60,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 32),
            Text(
              '没有SVG文档',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w700,
                color:
                    isDarkMode
                        ? AppTheme.darkTextColor
                        : AppTheme.textDarkColor,
                letterSpacing: -0.5,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '创建新的SVG文档或导入现有文件开始使用',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color:
                    isDarkMode
                        ? AppTheme.darkTextMediumColor
                        : AppTheme.textMediumColor,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 40),
            // 现代化的按钮设计
            Container(
              width: double.infinity,
              height: 56,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ElevatedButton.icon(
                onPressed: _createNewSvg,
                icon: const Icon(Icons.add, size: 20),
                label: const Text(
                  '创建新SVG',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: Colors.white,
                  shadowColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      AppTheme.borderRadiusMD,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              height: 56,
              decoration: BoxDecoration(
                border: Border.all(
                  color:
                      isDarkMode
                          ? AppTheme.darkBorderColor
                          : AppTheme.borderColor,
                  width: 1.5,
                ),
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
              ),
              child: OutlinedButton.icon(
                onPressed: _importSvgFile,
                icon: Icon(
                  Icons.download_outlined,
                  size: 20,
                  color:
                      isDarkMode
                          ? AppTheme.darkTextColor
                          : AppTheme.textDarkColor,
                ),
                label: Text(
                  '导入SVG文件',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color:
                        isDarkMode
                            ? AppTheme.darkTextColor
                            : AppTheme.textDarkColor,
                  ),
                ),
                style: OutlinedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      AppTheme.borderRadiusMD,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentsList() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.8,
          crossAxisSpacing: 16,
          mainAxisSpacing: 20,
        ),
        itemCount: _documents.length,
        itemBuilder: (context, index) {
          final document = _documents[index];

          return TweenAnimationBuilder<double>(
            duration: Duration(milliseconds: 300 + (index * 100)),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.translate(
                offset: Offset(0, 20 * (1 - value)),
                child: Opacity(
                  opacity: value,
                  child: Container(
                    decoration: BoxDecoration(
                      color:
                          isDarkMode
                              ? AppTheme.darkBgLightColor
                              : AppTheme.bgWhiteColor,
                      borderRadius: BorderRadius.circular(
                        AppTheme.borderRadiusLG,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color:
                              isDarkMode
                                  ? Colors.black.withValues(alpha: 0.3)
                                  : Colors.black.withValues(alpha: 0.08),
                          blurRadius: 16,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    clipBehavior: Clip.antiAlias,
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () => _editSvg(document),
                        borderRadius: BorderRadius.circular(
                          AppTheme.borderRadiusLG,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 预览区域
                            Expanded(
                              child: Container(
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      AppTheme.purpleLight.withValues(
                                        alpha: 0.3,
                                      ),
                                      AppTheme.bgWhiteColor,
                                    ],
                                  ),
                                ),
                                child: Center(
                                  child: Padding(
                                    padding: const EdgeInsets.all(20),
                                    child: SvgPicture.string(
                                      document.content,
                                      placeholderBuilder:
                                          (context) => Container(
                                            width: 40,
                                            height: 40,
                                            decoration: BoxDecoration(
                                              color: AppTheme.purpleDark
                                                  .withValues(alpha: 0.1),
                                              borderRadius:
                                                  BorderRadius.circular(
                                                    AppTheme.borderRadiusSM,
                                                  ),
                                            ),
                                            child: const Icon(
                                              Icons.image_outlined,
                                              color: AppTheme.purpleDark,
                                              size: 24,
                                            ),
                                          ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            // 信息区域
                            Container(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    document.title,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16,
                                      color:
                                          isDarkMode
                                              ? AppTheme.darkTextColor
                                              : AppTheme.textDarkColor,
                                      letterSpacing: -0.2,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 6),
                                  Text(
                                    '创建于: ${_formatDate(document.createdAt)}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color:
                                          isDarkMode
                                              ? AppTheme.darkTextMediumColor
                                              : AppTheme.textMediumColor,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      _buildModernActionButton(
                                        icon: Icons.edit_outlined,
                                        tooltip: '编辑',
                                        onPressed: () => _editSvg(document),
                                        color: AppTheme.primaryColor,
                                      ),
                                      const SizedBox(width: 8),
                                      _buildModernActionButton(
                                        icon: Icons.share_outlined,
                                        tooltip: '分享',
                                        onPressed: () => _shareSvg(document),
                                        color: AppTheme.secondaryColor,
                                      ),
                                      const SizedBox(width: 8),
                                      _buildModernActionButton(
                                        icon: Icons.image_outlined,
                                        tooltip: '分享为PNG',
                                        onPressed: () => _shareAsPng(document),
                                        color: AppTheme.greenDark,
                                      ),
                                      const SizedBox(width: 8),
                                      _buildModernActionButton(
                                        icon: Icons.delete_outline,
                                        tooltip: '删除',
                                        onPressed: () => _deleteSvg(document),
                                        color: AppTheme.redDark,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildModernActionButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSM),
      ),
      child: IconButton(
        icon: Icon(icon, size: 16),
        onPressed: onPressed,
        tooltip: tooltip,
        color: color,
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(),
      ),
    );
  }



  String _formatDate(DateTime dateTime) {
    final localDate = dateTime.toLocal();
    return '${localDate.year}-${localDate.month.toString().padLeft(2, '0')}-${localDate.day.toString().padLeft(2, '0')}';
  }
}
