import 'dart:convert';
import 'package:flutter/material.dart';
import '../config/app_settings.dart';
import '../config/app_theme.dart';
import '../config/chinese_traditional_colors.dart';
import '../config/constants.dart';
import '../markdown/models/markdown_watermark.dart';
import 'service_locator.dart';
import 'storage_service.dart';

class SettingsService {
  late StorageService _storage;
  late AppSettings _settings;

  // 主题变更回调
  Function(ThemeMode)? _onThemeChanged;

  // 获取实例，单例模式
  static final SettingsService _instance = SettingsService._internal();
  factory SettingsService() => _instance;
  SettingsService._internal();

  // 初始化
  Future<void> init() async {
    _storage = ServiceLocator().storageService;
    _loadSettings();
  }

  // 加载设置
  void _loadSettings() {
    final settingsJson = _storage.getString(AppConstants.keySettings);
    if (settingsJson != null) {
      try {
        final Map<String, dynamic> settingsMap = json.decode(settingsJson);
        _settings = AppSettings.fromJson(settingsMap);
        debugPrint('设置加载成功，主题模式: ${_settings.themeMode}');
      } catch (e) {
        debugPrint('加载设置失败: $e');
        _settings = AppSettings(); // 使用默认设置
      }
    } else {
      debugPrint('首次启动，使用默认设置，主题模式: ${AppSettings().themeMode}');
      _settings = AppSettings(); // 使用默认设置
    }
  }

  // 保存设置
  Future<void> saveSettings(AppSettings settings) async {
    _settings = settings;
    final settingsJson = json.encode(settings.toJson());
    await _storage.setString(AppConstants.keySettings, settingsJson);
  }

  // 更新设置
  Future<void> updateSettings({
    ThemeMode? themeMode,
    AppThemeType? themeType,
    double? aiTemperature,
    bool? privacyEnhanced,
    String? defaultModelId,
    int? requestTimeout,
    MarkdownWatermark? watermark,
  }) async {
    final newSettings = _settings.copyWith(
      themeMode: themeMode,
      themeType: themeType,
      aiTemperature: aiTemperature,
      privacyEnhanced: privacyEnhanced,
      defaultModelId: defaultModelId,
      requestTimeout: requestTimeout,
      watermark: watermark,
    );

    await saveSettings(newSettings);

    // 如果主题模式发生变化，触发回调
    if (themeMode != null && _onThemeChanged != null) {
      _onThemeChanged!(themeMode);
    }
  }

  // 设置主题变更回调
  void setThemeChangeCallback(Function(ThemeMode) callback) {
    _onThemeChanged = callback;
  }

  // 更新主题模式
  Future<void> updateThemeMode(ThemeMode themeMode) async {
    debugPrint('更新主题模式: $themeMode');
    await updateSettings(themeMode: themeMode);
  }

  // 更新水印设置
  Future<void> updateWatermark(MarkdownWatermark watermark) async {
    final newSettings = _settings.copyWith(watermark: watermark);
    await saveSettings(newSettings);
  }

  // 更新中国传统色主题
  Future<void> updateChineseTraditionalColorTheme(
    ChineseTraditionalColorTheme? theme,
  ) async {
    final newSettings = _settings.copyWith(chineseTraditionalColorTheme: theme);
    await saveSettings(newSettings);
  }

  // 获取当前设置
  AppSettings get settings => _settings;

  // 检查应用是否首次启动
  bool hasLaunchedBefore() {
    return _storage.getBool(AppConstants.keyHasLaunchedBefore) ?? false;
  }

  // 设置应用已启动过
  Future<void> setLaunchedBefore(bool value) async {
    await _storage.setBool(AppConstants.keyHasLaunchedBefore, value);
  }
}
