import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';

import '../config/app_theme.dart';
import '../content/content_home_page.dart';
import '../models/content_item.dart';
import '../services/content_service.dart';
import '../services/service_locator.dart';
import 'html_editor_screen.dart';

/// HTML管理屏幕
class HtmlManagerScreen extends StatefulWidget {
  /// 构造函数
  const HtmlManagerScreen({super.key});

  @override
  State<HtmlManagerScreen> createState() => _HtmlManagerScreenState();
}

class _HtmlManagerScreenState extends State<HtmlManagerScreen>
    with SingleTickerProviderStateMixin {
  /// HTML服务
  final _htmlService = ServiceLocator().htmlService;

  /// 内容服务
  final _contentService = ContentService();

  /// 动画控制器
  late final AnimationController _animationController;

  /// 透明度动画
  late final Animation<double> _fadeAnimation;

  /// 平移动画
  late final Animation<Offset> _slideAnimation;

  /// 是否正在导入
  bool _isImporting = false;

  /// 导入文件数量
  int _totalFiles = 0;

  /// 已导入文件数量
  int _importedFiles = 0;

  @override
  void initState() {
    super.initState();
    // 确保初始化内容服务
    _contentService.initialize();

    // 初始化动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
      ),
    );

    // 启动动画
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 创建新HTML
  void _createNewHtml() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const HtmlEditorScreen()),
    );
  }

  /// 导入HTML文件
  Future<void> _importHtmlFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['html', 'htm'],
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        // 显示加载指示器
        setState(() {
          _isImporting = true;
          _totalFiles = result.files.length;
          _importedFiles = 0;
        });

        for (final file in result.files) {
          if (file.path != null) {
            final htmlDoc = await _htmlService.importFromFile(File(file.path!));

            // 将导入的HTML保存到内容库
            await _contentService.createTextContent(
              title: htmlDoc!.title,
              type: ContentType.html,
              content: htmlDoc.content,
              tags: [],
            );

            setState(() {
              _importedFiles++;
            });
          }
        }

        // 完成导入
        setState(() {
          _isImporting = false;
        });

        // 显示成功提示
        if (mounted) {
          _showSuccessDialog(result.files.length);
        }
      }
    } catch (e) {
      // 重置导入状态
      setState(() {
        _isImporting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('导入HTML文件失败: $e'),
            backgroundColor: Colors.red.shade700,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// 显示导入成功对话框
  void _showSuccessDialog(int count) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.green.shade500,
                  size: 28,
                ),
                const SizedBox(width: 8),
                const Text('导入成功'),
              ],
            ),
            content: Text('已成功导入 $count 个HTML文件到内容库'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
              FilledButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ContentHomePage(),
                    ),
                  );
                },
                style: FilledButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                ),
                child: const Text('前往内容库'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor:
          isDarkMode ? AppTheme.darkBgColor : AppTheme.bgLightColor,
      appBar: AppBar(
        title: const Text(
          'HTML管理',
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 20),
        ),
        backgroundColor:
            isDarkMode ? AppTheme.darkBgLightColor : AppTheme.bgWhiteColor,
        foregroundColor:
            isDarkMode ? AppTheme.darkTextColor : AppTheme.textDarkColor,
        elevation: 0,
        scrolledUnderElevation: 1,
        shadowColor:
            isDarkMode ? AppTheme.darkBorderColor : AppTheme.borderColor,
        actions: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSM),
            ),
            child: IconButton(
              icon: const Icon(Icons.add, size: 22),
              onPressed: _isImporting ? null : _createNewHtml,
              tooltip: '新建HTML',
              color: AppTheme.primaryColor,
            ),
          ),
          Container(
            margin: const EdgeInsets.only(right: 8, left: 4),
            decoration: BoxDecoration(
              color: AppTheme.secondaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSM),
            ),
            child: IconButton(
              icon:
                  _isImporting
                      ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: AppTheme.secondaryColor,
                        ),
                      )
                      : const Icon(Icons.download, size: 22),
              onPressed: _isImporting ? null : _importHtmlFile,
              tooltip: _isImporting ? '导入中...' : '导入HTML',
              color: AppTheme.secondaryColor,
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          // 主内容
          Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(vertical: 32),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Container(
                    padding: const EdgeInsets.all(32),
                    constraints: const BoxConstraints(maxWidth: 400),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 现代化的图标设计
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            gradient: AppTheme.orangeGradient,
                            borderRadius: BorderRadius.circular(
                              AppTheme.borderRadiusXL,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.orangeDark.withValues(
                                  alpha: 0.3,
                                ),
                                blurRadius: 20,
                                offset: const Offset(0, 8),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.html,
                            size: 60,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 32),
                        Text(
                          'HTML管理',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                            color:
                                isDarkMode
                                    ? AppTheme.darkTextColor
                                    : AppTheme.textDarkColor,
                            letterSpacing: -0.5,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          '创建和编辑HTML文档，所有内容将自动保存至内容库进行统一管理',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16,
                            color:
                                isDarkMode
                                    ? AppTheme.darkTextMediumColor
                                    : AppTheme.textMediumColor,
                            height: 1.5,
                          ),
                        ),
                        const SizedBox(height: 40),
                        // 现代化的按钮设计
                        Container(
                          width: double.infinity,
                          height: 56,
                          decoration: BoxDecoration(
                            gradient: AppTheme.primaryGradient,
                            borderRadius: BorderRadius.circular(
                              AppTheme.borderRadiusMD,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.primaryColor.withValues(
                                  alpha: 0.3,
                                ),
                                blurRadius: 12,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: ElevatedButton.icon(
                            onPressed: _isImporting ? null : _createNewHtml,
                            icon: const Icon(Icons.add, size: 20),
                            label: const Text(
                              '创建新HTML',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              foregroundColor: Colors.white,
                              shadowColor: Colors.transparent,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusMD,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Container(
                          width: double.infinity,
                          height: 56,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color:
                                  isDarkMode
                                      ? AppTheme.darkBorderColor
                                      : AppTheme.borderColor,
                              width: 1.5,
                            ),
                            borderRadius: BorderRadius.circular(
                              AppTheme.borderRadiusMD,
                            ),
                          ),
                          child: OutlinedButton.icon(
                            onPressed: _isImporting ? null : _importHtmlFile,
                            icon: Icon(
                              Icons.download_outlined,
                              size: 20,
                              color:
                                  isDarkMode
                                      ? AppTheme.darkTextColor
                                      : AppTheme.textDarkColor,
                            ),
                            label: Text(
                              '导入HTML文件',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color:
                                    isDarkMode
                                        ? AppTheme.darkTextColor
                                        : AppTheme.textDarkColor,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              side: BorderSide.none,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusMD,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 32),
                        // 前往内容库链接
                        Container(
                          decoration: BoxDecoration(
                            color:
                                isDarkMode
                                    ? AppTheme.darkBgLightColor
                                    : AppTheme.primaryColor.withValues(
                                      alpha: 0.05,
                                    ),
                            borderRadius: BorderRadius.circular(
                              AppTheme.borderRadiusMD,
                            ),
                          ),
                          child: InkWell(
                            onTap:
                                _isImporting
                                ? null
                                : () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder:
                                              (context) =>
                                                  const ContentHomePage(),
                                        ),
                                      );
                                    },
                            borderRadius: BorderRadius.circular(
                              AppTheme.borderRadiusMD,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 16,
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.library_books_outlined,
                                    size: 20,
                                    color: AppTheme.primaryColor,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    '查看内容库',
                                    style: TextStyle(
                                      color: AppTheme.primaryColor,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16,
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Icon(
                                    Icons.arrow_forward_ios,
                                    size: 14,
                                    color: AppTheme.primaryColor,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),

          // 导入进度指示
          if (_isImporting)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color:
                      isDarkMode
                          ? AppTheme.darkBgLightColor
                          : AppTheme.bgWhiteColor,
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
                  boxShadow: [
                    BoxShadow(
                      color:
                          isDarkMode
                              ? Colors.black.withValues(alpha: 0.3)
                              : Colors.black.withValues(alpha: 0.1),
                      blurRadius: 16,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2.5,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            '正在导入HTML文件 ($_importedFiles/$_totalFiles)',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                              color:
                                  isDarkMode
                                      ? AppTheme.darkTextColor
                                      : AppTheme.textDarkColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(
                        AppTheme.borderRadiusSM,
                      ),
                      child: LinearProgressIndicator(
                        value:
                            _totalFiles > 0 ? _importedFiles / _totalFiles : 0,
                        backgroundColor:
                            isDarkMode
                                ? AppTheme.darkBorderColor
                                : AppTheme.borderColor,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppTheme.primaryColor,
                        ),
                        minHeight: 6,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
